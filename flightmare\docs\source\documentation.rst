Flightmare documentation
========================

Welcome to Flightmare's documentation!

This home page contains an index with a brief description of the different sections in the documentation. 
Feel free to read in whatever order you prefer. 
In any case, here are a few suggestions for newcomers.

* :ref:`Installing and getting started with Flightmare <quick-start>`

Getting started
---------------

| :ref:`Introduction <introduction>` — What to expect from Flightmare.
| :ref:`Quick start <quick-start>` — Get the latest Flightmare releases.
| :ref:`F.A.Q. <faq-install>` — Some of the most frequent installation issues.

Building Flightmare binary
--------------------------

| :ref:`Standalone build <standalone-build>` — Make the build in Unity.
| :ref:`F.A.Q. <faq-binary>` — Some of the most frequent building issues.

First steps
-----------

| :ref:`Core concepts <core-concepts>`  — Overview of the basic concepts in Flightmare.
| :ref:`Server and client <server-client>` — Manage and access the simulation.
| :ref:`Quadrotor and Objects <quad-objects>` — Learn about quadrotors and objects and how to handle them.
| :ref:`Environments and navigation <environments-navigation>` — Discover the different maps and how to navigate through waypoints.
| :ref:`Sensors and data <sensors-data>` — Retrieve simulation data using sensors.
| :ref:`Point Cloud <point-cloud>` - Retrieve the point cloud from the scene.
| :ref:`F.A.Q. <faq-first-steps>` — Some of the most frequent usage issues.

Advanced steps
--------------

| :ref:`Advanced concepts <advanced-concepts>` — Overview of all advanced steps
| :ref:`Motion planning <adv-motion-planning>` — Learn about connecting OMPL and Flightmare

Tutorials — General
-------------------

| :ref:`Pilot Tutorial <tut-pilot>` — Use Gazebo dynamics with Flightmare.
| :ref:`Racing Tutorial <tut-racing>` — Navigate through gates.
| :ref:`Retrieve simulation data <tut-camera>` — A step by step guide to properly gather data.

Tutorials — Assets
------------------
| [Todo] Add a new environment — Create and add a new environment to Flightmare.
| [Todo] Add a new vehicle — Prepare a vehicle to be used in Flightmare.
| [Todo] Add new objects — Import additional props into Flightmare.
| [Todo] Map customization — Edit an existing map.
| [Todo] Material customization — Edit vehicle and building materials.

Tutorials — Developers
----------------------

| [Todo] Create a sensor — Develop a new sensor to be used in Flightmare.
| [Todo] Make a release — For developers who want to publish a release.
| [Todo] Generate detailed colliders — Create detailed colliders for meshes.

C++ References
--------------

|  :ref:`Quadrotor references <cpp-quad-ref>` — All functions of quadrotor.
|  :ref:`Camera references <cpp-camera-ref>` — All functions of camera.
|  :ref:`Gate references <cpp-gate-ref>` — All functions of gate.
|  :ref:`Quadrotor Environment <cpp-quad-env-ref>` — All OpenAIGym environments function.

Python References
-----------------

|  :ref:`Wrapper <py-flight-env-vec-ref>` — Wrapper functions of QuadrotorEnv.


Contributing
------------

| [Todo] Contribution guidelines — The different ways to contribute to Flightmare.
| [Todo] Code of conduct — Standard rights and duties for contributors.
| [Todo] Coding standard — Guidelines to write proper code.
| [Todo] Documentation standard — Guidelines to write proper documentation.