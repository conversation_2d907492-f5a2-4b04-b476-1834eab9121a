name: CPP_CI

on: [push, pull_request]

jobs:
  build:
    runs-on: ubuntu-latest
    container: realyunlong/flightmare_dep
    
    steps:
    - uses: actions/checkout@v2
    
    - name: cmake
      run: pwd && cd flightlib/build && cmake -DBUILD_TEST=ON -DEIGEN_FROM_SYSTEM=OFF ..
    - name: check_file
      run: cd /__w/flightmare/flightmare && ls && cd ./flightlib && ls && cd ./configs && cat quadrotor_env.yaml
    - name: make
      run: cd flightlib/build && make -j${nprog}
    - name: test
      env: 
        FLIGHTMARE_PATH: /__w/flightmare/flightmare
      run: cd flightlib/build && ./test_lib
