goal_pos: [-1.1, -1.6, 3.6]
render_offset: [50.0, 50.0, 50.0]
start_pos:  [-5.0, -4.5, 3.6]
# 
gates:
  Gate1:
    position: [-0.9, -1.27, 3.48]
    rotation: [0.7071068, 0, 0, 0.7071068]
    scale: [0.604, 0.1, 0.604]
    uncertainty: [0.0, 0.0, 0.0, 0.0]
  Gate2:
    position: [9.09, 6.26, 1.08]
    rotation: [0.7071068, 0, 0, 0.7071068]
    scale: [0.604, 0.1, 0.604]
    uncertainty: [0.0, 0.0, 0.0, 0.0]
  Gate3:
    position:  [9.27, -3.46, 1.17]
    rotation: [ 0.8910065, 0, 0, -0.4539905]
    scale: [0.604, 0.1, 0.604]
    uncertainty: [0.0, 0.0, 0.0, 0.0]
  Gate4:
    position: [-4.5, -6.14, 3.4]
    rotation: [  0.7071068 , 0, 0, -0.7071068]
    scale: [0.604, 0.1, 0.604]
    uncertainty: [0.0, 0.0, 0.0, 0.0]
  Gate5:
    position: [-4.48, -5.94, 1.05]
    rotation: [0.7071068, 0, 0, 0.7071068]
    scale: [0.604, 0.1, 0.604]
    uncertainty: [0.0, 0.0, 0.0, 0.0]
  Gate6:
    position: [4.45, -0.8, 1.09]
    rotation: [ 0.0174524, 0, 0, 0.9998477]
    scale: [0.604, 0.1, 0.604]
    uncertainty: [0.0, 0.0, 0.0, 0.0]
  Gate7:
    position: [-2.65, 6.51, 1.3]
    rotation: [ 0.8038569, 0, 0, -0.5948228]
    scale: [0.604, 0.1, 0.604]
    uncertainty: [0.0, 0.0, 0.0, 0.0]
  N: 7
