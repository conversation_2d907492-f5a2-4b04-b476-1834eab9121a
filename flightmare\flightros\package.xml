<?xml version="1.0"?>
<package format="2">
  <name>flightros</name>
  <version>0.0.1</version>
  <description>ROS-Wrapper for flightlib</description>

  <maintainer email="<EMAIL>"><PERSON><PERSON></maintainer>

  <license>GNU GPL</license>

  <buildtool_depend>catkin</buildtool_depend>
  <buildtool_depend>catkin_simple</buildtool_depend>

  <depend>flightlib</depend>
  <depend>roscpp</depend>
  <depend>autopilot</depend>
  <depend>quadrotor_common</depend>
  <depend>nav_msgs</depend>
  <depend>eigen_catkin</depend>
  <depend>quadrotor_msgs</depend>
  <depend>polynomial_trajectories</depend>
  <depend>std_msgs</depend>
  <depend>geometry_msgs</depend>
  <depend>tf</depend>
  <depend>tf_conversions</depend>
  <depend>eigen_conversions</depend>
  <depend>image_transport</depend>
  <depend>cv_bridge</depend>
  <depend>mav_msgs</depend>

</package>