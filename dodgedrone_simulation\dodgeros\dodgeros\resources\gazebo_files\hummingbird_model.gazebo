<?xml version="1.0"?>
<!--
  Copyright 2015 <PERSON><PERSON><PERSON>, ASL, ETH Zurich, Switzerland
  Copyright 2015 <PERSON>, ASL, ETH Zurich, Switzerland
  Copyright 2015 <PERSON>, ASL, ETH Zurich, Switzerland
  Copyright 2015 <PERSON><PERSON><PERSON>, ASL, ETH Zurich, Switzerland
  Copyright 2015 <PERSON>, ASL, ETH Zurich, Switzerland

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
-->

<robot name="$(arg mav_name)" xmlns:xacro="http://ros.org/wiki/xacro">
  <!-- Instantiate the mav-->
  <xacro:include filename="$(find dodgeros)/resources/gazebo_files/hummingbird_base.xacro" />

  <!-- Mount a generic odometry sensor without odometry map (working everywhere). -->
  <xacro:odometry_plugin_macro
    namespace="${namespace}"
    odometry_sensor_suffix="1"
    parent_link="${namespace}/base_link"
    pose_topic="odometry_sensor1/pose"
    pose_with_covariance_topic="odometry_sensor1/pose_with_covariance"
    position_topic="odometry_sensor1/position"
    transform_topic="odometry_sensor1/transform"
    odometry_topic="odometry_sensor1/odometry"
    parent_frame_id="world"
    child_frame_id="${namespace}/odometry_sensor1"
    mass_odometry_sensor="0.00001"
    measurement_divisor="5"
    measurement_delay="0"
    unknown_delay="0.0"
    noise_normal_position="0 0 0"
    noise_normal_quaternion="0 0 0"
    noise_normal_linear_velocity="0 0 0"
    noise_normal_angular_velocity="0 0 0"
    noise_uniform_position="0 0 0"
    noise_uniform_quaternion="0 0 0"
    noise_uniform_linear_velocity="0 0 0"
    noise_uniform_angular_velocity="0 0 0"
    enable_odometry_map="false"
    odometry_map=""
    image_scale="">
    <inertia ixx="0.00001" ixy="0.0" ixz="0.0" iyy="0.00001" iyz="0.0" izz="0.00001" /> <!-- [kg m^2] [kg m^2] [kg m^2] [kg m^2] [kg m^2] [kg m^2] -->
    <origin xyz="0.0 0.0 0.0" rpy="0.0 0.0 0.0" />
  </xacro:odometry_plugin_macro>
</robot>

