# General Parameters of the Quad and the Environment
General:
  air_density: 1.204       # kg/m^3


# This information is used by the BEMPropeller model
Propeller:
  ## Propeller Geometry ##
  # Radius of the propeller disk
  radius: 6.477e-2    # m

  # To be measured at the innermost point
  pitch: 22.95       # deg

  # twist = (pitch_outside - pitch_inside)/blade length
  twist: -8.0       # deg/m

  # Use mean value here
  chord_inner: 1.7e-2      # m
  chord_outer: 0.8e-2      # m

  # Ratio of area covered by blades
  solidity: 0.215       # 1

  # Distance between CoG of blade and hinge
  distance_cog: 2.7e-2      # m

  # Number of blades
  num_blades: 3           # 1

  # Mass of a _single_ blade
  mass_blade: 1.22e-3     # kg

  # Offset of the virtual hinge spring (0.1 should mostly be fine)
  rel_hinge_offset: 0.1         # 1


  ## Aerodynamic Parameters ##
  # All coefficients can be calculated using the MATLAB script
  # "Propeller_SysID.m"
  drag_coefficient: 4.168863    # 1
  lift_coefficient: 4.797071    # 1
  hinge_spring_constant: 5.89        # Nm/rad


# This information is used by the MotorModel
Motor:
  ## General Parameters ##
  # Can be measured using Event-Camera High-Speed movie
  # Average spin-up / spin-down time constant
  time_constant: 33.0e-3     # s
  # Inertia of the motor
  inertia: 9.3575e-6   # kg*m^2


# This information is used by the RigidBody and AerodynamicDrag Model
Quadcopter:
  ## General Parameters ##
  mass: 0.752       # kg
  frontarea_x: 1.5e-2      # m^2
  frontarea_y: 1.5e-2      # m^2
  frontarea_z: 3.0e-2      # m^2

  ## Aerodynamic Properties ##
  horizontal_drag_coefficient: 1.04        # drag of a cube
  vertical_drag_coefficient: 1.04        # drag of a cube


# This information is used by the NeuralNet Model
NeuralNet:
  weight_file: ""
