/*
 * Copyright 2015 <PERSON><PERSON><PERSON>, ASL, ETH Zurich, Switzerland
 * Copyright 2015 <PERSON>, ASL, ETH Zurich, Switzerland
 * Copyright 2015 <PERSON>, ASL, ETH Zurich, Switzerland
 * Copyright 2015 <PERSON>, ASL, ETH Zurich, Switzerland
 * Copyright 2015 <PERSON>, ASL, ETH Zurich, Switzerland
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0

 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef MAV_MSGS_DEFAULT_VALUES_H_
#define MAV_MSGS_DEFAULT_VALUES_H_

#include <mav_msgs/common.h>

namespace mav_msgs {

const double kZurichLatitude = 0.8267;
const double kZurichHeight = 405.94;
const double kGravity = MagnitudeOfGravity(k<PERSON><PERSON>chHeight, kZurichLatitude);
}

#endif /* MAV_MSGS_DEFAULT_VALUES_H_ */
