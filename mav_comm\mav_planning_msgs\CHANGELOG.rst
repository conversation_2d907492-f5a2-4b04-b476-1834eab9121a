^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
Changelog for package mav_planning_msgs
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
3.3.3 (2019-08-16)
------------------
* Add PolynomialSegment/Trajectory messages with 3D rotation vector.
* Update conversions, deprecate old conversions.
* Update planner service to take 4D or full trajectory message

3.3.2 (2018-08-22)
------------------
* Fix indigo eigen3 compatibility.

3.3.1 (2018-08-21)
------------------
* Fix Eigen3 warning. Migration from Jade.
* Add std_msgs, eigen and cmake_modules dependencies.

3.3.0 (2018-08-17)
------------------
* Add 2D polygon messages and services.
* Add ROS and Eigen conversions.
* Add standard services and trajectory messages.
* Add mav_planning_msgs
* Contributors: <PERSON>, <PERSON><PERSON>
