<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QuadWidget</class>
 <widget class="QWidget" name="QuadWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>400</width>
    <height>80</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Minimum" vsizetype="Minimum">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>400</width>
    <height>80</height>
   </size>
  </property>
  <property name="windowTitle">
   <string>Copilot</string>
  </property>
  <widget class="QLabel" name="title">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>10</y>
     <width>291</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>18</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>DodgeDrone GUI</string>
   </property>
  </widget>
  <widget class="QPushButton" name="button_connect">
   <property name="geometry">
    <rect>
     <x>310</x>
     <y>50</y>
     <width>80</width>
     <height>27</height>
    </rect>
   </property>
   <property name="text">
    <string>Connect</string>
   </property>
  </widget>
  <widget class="QLabel" name="label">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>50</y>
     <width>141</width>
     <height>27</height>
    </rect>
   </property>
   <property name="sizePolicy">
    <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
     <horstretch>0</horstretch>
     <verstretch>0</verstretch>
    </sizepolicy>
   </property>
   <property name="font">
    <font>
     <pointsize>11</pointsize>
    </font>
   </property>
   <property name="text">
    <string>Quad Namespace:</string>
   </property>
  </widget>
  <widget class="QLineEdit" name="namespace_text">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>50</y>
     <width>159</width>
     <height>27</height>
    </rect>
   </property>
   <property name="toolTip">
    <string>Topic to send Twist message to</string>
   </property>
   <property name="text">
    <string/>
   </property>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
