<?xml version="1.0"?>

<!-- Flightmare launch -->

<launch>
    <arg name="quad_name" default="kingfisher"/>
    <arg name="use_bem_propeller_model" default="False"/>
    <arg name="low_level_controller" default="Simple"/>
    <arg name="render" default="False"/>
    <param name="/use_sim_time" value="True"/>
    <arg name="real_time_factor" default="1.0"/>
    <arg name="gui" default="True"/>
    <arg name="rviz" default="True"/>

    <!-- quadrotor simulation -->
    <group ns="$(arg quad_name)">
        <node name="dodgeros_pilot" pkg="envsim" type="visionsim_node" output="screen">
            <param name="agi_param_dir" value="$(find dodgelib)/params"/>
            <param name="ros_param_dir" value="$(find envsim)/parameters"/>
            <param name="use_bem_propeller_model" value="$(arg use_bem_propeller_model)"/>
            <param name="pilot_config" value="simple_sim_pilot.yaml"/>
            <param name="real_time_factor" value="$(arg real_time_factor)"/>
            <param name="low_level_controller" value="$(arg low_level_controller)"/>
            <param name="camera_config" value="$(find envsim)/parameters/camera_config.yaml"/>
            <param name="render" value="$(arg render)"/>
        </node>
    </group>

    <!-- visualization -->
    <node pkg="rviz" type="rviz" name="viz_face" args="-d $(find envsim)/resources/rviz/envsim.rviz"
          ns="$(arg quad_name)" if="$(arg rviz)"/>

    <!-- gui (optional/unused for this project) -->
    <node name="dodgeros_gui" pkg="rqt_gui" type="rqt_gui"
          args="-s dodgeros_gui.basic_flight.BasicFlight --args --quad_name $(arg quad_name)" output="screen"
          if="$(arg gui)"/>
    
    <!-- flightmare standalone -->
    <node name="flight_render" pkg="flightrender" type="vitfly-unity.x86_64" if="$(arg render)"/>
    <!-- args="-batchmode"/> -->

</launch>
