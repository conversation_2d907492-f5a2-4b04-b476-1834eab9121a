<?xml version="1.0"?>

<robot name="kingfisher" xmlns:xacro="http://ros.org/wiki/xacro">
  <!-- Properties -->
  <xacro:property name="namespace" value="$(arg namespace)" />
  <xacro:property name="rotor_velocity_slowdown_sim" value="10" />
  <xacro:property name="use_mesh_file" value="true" />
  <xacro:property name="mesh_file" value="package://rotors_description/meshes/iris.dae" />
  <xacro:property name="mass" value="0.752" /> <!-- [kg] -->
  <xacro:property name="body_width" value="0.09" /> <!-- [m] -->
  <xacro:property name="body_height" value="0.09" /> <!-- [m] -->
  <xacro:property name="mass_rotor" value="0.0042" /> <!-- [kg] -->
  <xacro:property name="arm_length_front_x" value="0.11" /> <!-- [m] -->
  <xacro:property name="arm_length_back_x" value="0.11" /> <!-- [m] -->
  <xacro:property name="arm_length_front_y" value="0.11" /> <!-- [m] -->
  <xacro:property name="arm_length_back_y" value="0.11" /> <!-- [m] -->
  <xacro:property name="rotor_offset_top" value="0.027" /> <!-- [m] -->
  <xacro:property name="radius_rotor" value="0.127" /> <!-- [m] -->
  <xacro:property name="motor_constant" value="1.56252e-06" /> <!-- [kg.m/s^2] -->
  <xacro:property name="moment_constant" value="0.022" /> <!-- [m] -->
  <xacro:property name="time_constant_up" value="0.033" /> <!-- [s] -->
  <xacro:property name="time_constant_down" value="0.033" /> <!-- [s] -->
  <xacro:property name="max_rot_velocity" value="2800" /> <!-- [rad/s] -->
  <xacro:property name="sin30" value="0.5" />
  <xacro:property name="cos30" value="0.866025403784" />
  <xacro:property name="sqrt2" value="1.4142135623730951" />
  <xacro:property name="rotor_drag_coefficient" value="8.06428e-05" />
  <xacro:property name="rolling_moment_coefficient" value="0.000001" />

  <!-- Property Blocks -->
  <xacro:property name="body_inertia">
    <inertia ixx="0.0025" ixy="0.0" ixz="0.0" iyy="0.0021" iyz="0.0" izz="0.0043" /> <!-- [kg.m^2] [kg.m^2] [kg.m^2] [kg.m^2] [kg.m^2] [kg.m^2] -->
  </xacro:property>

  <!-- inertia of a single rotor, assuming it is a cuboid. Height=3mm, width=15mm -->
  <xacro:property name="rotor_inertia">
    <xacro:box_inertia x="${radius_rotor}" y="0.015" z="0.003" mass="${mass_rotor*rotor_velocity_slowdown_sim}" />
  </xacro:property>

  <!-- Included URDF Files -->
  <xacro:include filename="$(find rotors_description)/urdf/multirotor_base.xacro" />

  <!-- Instantiate multirotor_base_macro once -->
  <xacro:multirotor_base_macro
    robot_namespace="${namespace}"
    mass="${mass}"
    body_width="${body_width}"
    body_height="${body_height}"
    use_mesh_file="${use_mesh_file}"
    mesh_file="${mesh_file}"
    >
    <xacro:insert_block name="body_inertia" />
  </xacro:multirotor_base_macro>

  <!-- Instantiate rotors -->
  <xacro:vertical_rotor
    robot_namespace="${namespace}"
    suffix="front_right"
    direction="ccw"
    motor_constant="${motor_constant}"
    moment_constant="${moment_constant}"
    parent="${namespace}/base_link"
    mass_rotor="${mass_rotor}"
    radius_rotor="${radius_rotor}"
    time_constant_up="${time_constant_up}"
    time_constant_down="${time_constant_down}"
    max_rot_velocity="${max_rot_velocity}"
    motor_number="0"
    rotor_drag_coefficient="${rotor_drag_coefficient}"
    rolling_moment_coefficient="${rolling_moment_coefficient}"
    color="Red"
    use_own_mesh="false"
    mesh="">
    <origin xyz="${arm_length_front_x} -${arm_length_front_y} ${rotor_offset_top}" rpy="0 0 0" />
    <xacro:insert_block name="rotor_inertia" />
  </xacro:vertical_rotor>

  <xacro:vertical_rotor
    robot_namespace="${namespace}"
    suffix="back_left"
    direction="ccw"
    motor_constant="${motor_constant}"
    moment_constant="${moment_constant}"
    parent="${namespace}/base_link"
    mass_rotor="${mass_rotor}"
    radius_rotor="${radius_rotor}"
    time_constant_up="${time_constant_up}"
    time_constant_down="${time_constant_down}"
    max_rot_velocity="${max_rot_velocity}"
    motor_number="1"
    rotor_drag_coefficient="${rotor_drag_coefficient}"
    rolling_moment_coefficient="${rolling_moment_coefficient}"
    color="Blue"
    use_own_mesh="false"
    mesh="">
    <origin xyz="-${arm_length_back_x} ${arm_length_back_y} ${rotor_offset_top}" rpy="0 0 0" />
    <xacro:insert_block name="rotor_inertia" />
  </xacro:vertical_rotor>

  <xacro:vertical_rotor robot_namespace="${namespace}"
    suffix="front_left"
    direction="cw"
    motor_constant="${motor_constant}"
    moment_constant="${moment_constant}"
    parent="${namespace}/base_link"
    mass_rotor="${mass_rotor}"
    radius_rotor="${radius_rotor}"
    time_constant_up="${time_constant_up}"
    time_constant_down="${time_constant_down}"
    max_rot_velocity="${max_rot_velocity}"
    motor_number="2"
    rotor_drag_coefficient="${rotor_drag_coefficient}"
    rolling_moment_coefficient="${rolling_moment_coefficient}"
    color="Blue"
    use_own_mesh="false"
    mesh="">
    <origin xyz="-${arm_length_back_x} -${arm_length_back_y} ${rotor_offset_top}" rpy="0 0 0" />
    <xacro:insert_block name="rotor_inertia" />
  </xacro:vertical_rotor>

  <xacro:vertical_rotor robot_namespace="${namespace}"
    suffix="back_right"
    direction="cw"
    motor_constant="${motor_constant}"
    moment_constant="${moment_constant}"
    parent="${namespace}/base_link"
    mass_rotor="${mass_rotor}"
    radius_rotor="${radius_rotor}"
    time_constant_up="${time_constant_up}"
    time_constant_down="${time_constant_down}"
    max_rot_velocity="${max_rot_velocity}"
    motor_number="3"
    rotor_drag_coefficient="${rotor_drag_coefficient}"
    rolling_moment_coefficient="${rolling_moment_coefficient}"
    color="Red"
    use_own_mesh="false"
    mesh="">
    <origin xyz="${arm_length_front_x} ${arm_length_front_y} ${rotor_offset_top}" rpy="0 0 0" />
    <xacro:insert_block name="rotor_inertia" />
  </xacro:vertical_rotor>

</robot>
