.. Flightmare documentation master file, created by
   sphinx-quickstart on Tue Oct 20 16:02:55 2020.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.


.. include:: documentation.rst


.. toctree::
    :hidden:
    :maxdepth: 1

    Home <documentation>

.. toctree::
    :hidden:
    :maxdepth: 2
    :caption: Getting Started

    Introduction <getting_started/readme>
    Quick Start Installation <getting_started/quick_start>
    F.A.Q. <getting_started/faq>

.. toctree::
    :hidden:
    :maxdepth: 2
    :caption: Building Flightmare Binary

    Standalone build <building_flightmare_binary/standalone>
    F.A.Q. <building_flightmare_binary/faq>

.. toctree::
    :hidden:
    :maxdepth: 2
    :caption: First steps

    Core Concepts <first_steps/core_concepts>
    Server & Client <first_steps/server_and_client>
    Quadrotor & Objects <first_steps/quad_and_objects>
    Environments & Navigation <first_steps/envs_and_navigation>
    Sensors & Data <first_steps/sensors_and_data>
    PointCloud <first_steps/pointcloud>
    F.A.Q. <first_steps/faq>

.. toctree::
    :hidden:
    :maxdepth: 2
    :caption: Advanced steps

    Advanced concepts <advanced_steps/advanced_concepts>
    Motion Planning <advanced_steps/motion_planning>


.. toctree::
    :hidden:
    :maxdepth: 2
    :caption: Tutorial - General

    Pilot <tutorials_general/pilot>
    Navigation <tutorials_general/racing>
    Retrieve data <tutorials_general/camera>

.. toctree::
    :hidden:
    :maxdepth: 2
    :caption: Tutorial - Assets


.. toctree::
    :hidden:
    :maxdepth: 2
    :caption: Tutorial - Developers

.. toctree::
    :hidden:
    :maxdepth: 2
    :caption: C++ References
    
    Quadrotor references <cpp_references/quadrotor>
    Camera references <cpp_references/camera>
    Gate references <cpp_references/gate>
    Quadrotor Environment <cpp_references/quadrotor_env>

.. toctree::
    :hidden:
    :maxdepth: 2
    :caption: Python References
    
    Wrapper <python_references/flight_env_vec>

.. toctree::
    :hidden:
    :maxdepth: 2
    :caption: Contribution


