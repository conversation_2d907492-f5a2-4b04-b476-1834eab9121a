<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QuadWidget</class>
 <widget class="QWidget" name="QuadWidget">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>510</width>
    <height>500</height>
   </rect>
  </property>
  <property name="sizePolicy">
   <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
    <horstretch>0</horstretch>
    <verstretch>0</verstretch>
   </sizepolicy>
  </property>
  <property name="minimumSize">
   <size>
    <width>510</width>
    <height>500</height>
   </size>
  </property>
  <property name="baseSize">
   <size>
    <width>0</width>
    <height>0</height>
   </size>
  </property>
  <property name="font">
   <font>
    <weight>50</weight>
    <bold>false</bold>
    <kerning>false</kerning>
   </font>
  </property>
  <property name="windowTitle">
   <string>Copilot</string>
  </property>
  <property name="autoFillBackground">
   <bool>false</bool>
  </property>
  <widget class="QLabel" name="label_2">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>290</y>
     <width>161</width>
     <height>17</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>Odometry Estimate</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_7">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>400</y>
     <width>271</width>
     <height>17</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>Reference State</string>
   </property>
  </widget>
  <widget class="QLabel" name="pilot_title">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>0</y>
     <width>211</width>
     <height>31</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <pointsize>12</pointsize>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>Pilot</string>
   </property>
  </widget>
  <widget class="QPushButton" name="button_arm_bridge">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>40</y>
     <width>91</width>
     <height>27</height>
    </rect>
   </property>
   <property name="cursor">
    <cursorShape>ArrowCursor</cursorShape>
   </property>
   <property name="text">
    <string>Arm Bridge</string>
   </property>
  </widget>
  <widget class="QPushButton" name="button_land">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>80</y>
     <width>91</width>
     <height>27</height>
    </rect>
   </property>
   <property name="cursor">
    <cursorShape>ArrowCursor</cursorShape>
   </property>
   <property name="text">
    <string>Land</string>
   </property>
  </widget>
  <widget class="QPushButton" name="button_start">
   <property name="geometry">
    <rect>
     <x>110</x>
     <y>40</y>
     <width>91</width>
     <height>27</height>
    </rect>
   </property>
   <property name="cursor">
    <cursorShape>ArrowCursor</cursorShape>
   </property>
   <property name="toolTip">
    <string>Reset velocities to zero</string>
   </property>
   <property name="text">
    <string>Start</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_9">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>150</y>
     <width>141</width>
     <height>17</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="lineWidth">
    <number>1</number>
   </property>
   <property name="text">
    <string>Pilot Status</string>
   </property>
  </widget>
  <widget class="QPushButton" name="button_force_hover">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>80</y>
     <width>91</width>
     <height>27</height>
    </rect>
   </property>
   <property name="cursor">
    <cursorShape>ArrowCursor</cursorShape>
   </property>
   <property name="mouseTracking">
    <bool>false</bool>
   </property>
   <property name="text">
    <string>Force Hover</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_13">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>240</y>
     <width>191</width>
     <height>17</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="lineWidth">
    <number>1</number>
   </property>
   <property name="text">
    <string>Low-Level Telemetry</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_10">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>170</y>
     <width>131</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>Bridge:</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_12">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>210</y>
     <width>161</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>Cont. Comp. TIme (ms):</string>
   </property>
  </widget>
  <widget class="QLabel" name="control_computation_time">
   <property name="geometry">
    <rect>
     <x>350</x>
     <y>210</y>
     <width>41</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>0.0</string>
   </property>
  </widget>
  <widget class="QLabel" name="bridge_name">
   <property name="geometry">
    <rect>
     <x>90</x>
     <y>170</y>
     <width>121</width>
     <height>17</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>None</string>
   </property>
  </widget>
  <widget class="QLabel" name="legend_battery">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>260</y>
     <width>140</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>Battery Voltage [V]</string>
   </property>
  </widget>
  <widget class="QLabel" name="status_battery_voltage">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>260</y>
     <width>201</width>
     <height>17</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>12.56 Volts</string>
   </property>
  </widget>
  <widget class="QLabel" name="legend_state_est_velocity">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>330</y>
     <width>140</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>Velocity [m/s]</string>
   </property>
  </widget>
  <widget class="QLabel" name="legend_state_est_orientation">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>350</y>
     <width>161</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>Roll, Pitch, Heading [°]</string>
   </property>
  </widget>
  <widget class="QLabel" name="legend_state_est_position">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>310</y>
     <width>140</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>Position [m]</string>
   </property>
  </widget>
  <widget class="QLabel" name="state_est_orientation">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>350</y>
     <width>241</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>0.00, 0.00, 0.00</string>
   </property>
  </widget>
  <widget class="QLabel" name="state_est_position">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>310</y>
     <width>241</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string notr="true">0.00, 0.00, 0.00</string>
   </property>
   <property name="textFormat">
    <enum>Qt::AutoText</enum>
   </property>
   <property name="alignment">
    <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
   </property>
   <property name="margin">
    <number>0</number>
   </property>
   <property name="indent">
    <number>-1</number>
   </property>
  </widget>
  <widget class="QLabel" name="state_est_velocity">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>330</y>
     <width>241</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>0.00, 0.00, 0.00</string>
   </property>
  </widget>
  <widget class="QLabel" name="legend_state_des_velocity">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>440</y>
     <width>140</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>Velocity [m/s]</string>
   </property>
   <property name="scaledContents">
    <bool>false</bool>
   </property>
  </widget>
  <widget class="QLabel" name="legend_state_des_yaw">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>480</y>
     <width>140</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>Body Rates [°/s]</string>
   </property>
  </widget>
  <widget class="QLabel" name="legend_state_des_acceleration">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>460</y>
     <width>161</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>Roll, Pitch, Heading [°]</string>
   </property>
  </widget>
  <widget class="QLabel" name="legend_state_des_position">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>420</y>
     <width>140</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>Position [m]</string>
   </property>
  </widget>
  <widget class="QLabel" name="ref_orientation">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>460</y>
     <width>241</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>0.00, 0.00, 0.00</string>
   </property>
  </widget>
  <widget class="QLabel" name="ref_position">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>420</y>
     <width>241</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>0.00, 0.00, 0.00</string>
   </property>
  </widget>
  <widget class="QLabel" name="ref_velocity">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>440</y>
     <width>241</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>0.00, 0.00, 0.00</string>
   </property>
  </widget>
  <widget class="QLabel" name="ref_body_rates">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>480</y>
     <width>241</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>0.00, 0.00, 0.00</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_14">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>190</y>
     <width>131</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>Traj. Dur. Left (s):</string>
   </property>
  </widget>
  <widget class="QLabel" name="label_15">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>210</y>
     <width>131</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>N Traj. in Queue:</string>
   </property>
  </widget>
  <widget class="QLabel" name="trajectory_execution_left_duration">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>190</y>
     <width>31</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>123</string>
   </property>
  </widget>
  <widget class="QLabel" name="trajectories_left_in_queue">
   <property name="geometry">
    <rect>
     <x>150</x>
     <y>210</y>
     <width>41</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>0</string>
   </property>
  </widget>
  <widget class="QLabel" name="legend_state_est_body_rates">
   <property name="geometry">
    <rect>
     <x>20</x>
     <y>370</y>
     <width>161</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>Body Rates [°/s]</string>
   </property>
  </widget>
  <widget class="QLabel" name="state_est_body_rates">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>370</y>
     <width>241</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>0.00, 0.00, 0.00</string>
   </property>
  </widget>
  <widget class="QLabel" name="state_est_frame_id">
   <property name="geometry">
    <rect>
     <x>190</x>
     <y>290</y>
     <width>201</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>Frame ID:</string>
   </property>
  </widget>
  <widget class="QLabel" name="bridge_state">
   <property name="geometry">
    <rect>
     <x>230</x>
     <y>170</y>
     <width>171</width>
     <height>17</height>
    </rect>
   </property>
   <property name="font">
    <font>
     <weight>75</weight>
     <bold>true</bold>
    </font>
   </property>
   <property name="text">
    <string>None</string>
   </property>
  </widget>
  <widget class="QLabel" name="click_status">
   <property name="geometry">
    <rect>
     <x>100</x>
     <y>5</y>
     <width>281</width>
     <height>17</height>
    </rect>
   </property>
   <property name="text">
    <string>no message</string>
   </property>
  </widget>
  <widget class="QWidget" name="layoutWidget">
   <property name="geometry">
    <rect>
     <x>10</x>
     <y>117</y>
     <width>491</width>
     <height>27</height>
    </rect>
   </property>
   <layout class="QHBoxLayout" name="horizontalLayout">
    <item>
     <widget class="QLabel" name="label_go_to_pose_x">
      <property name="text">
       <string>X:</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="go_to_pose_x">
      <property name="font">
       <font>
        <pointsize>9</pointsize>
        <weight>50</weight>
        <bold>false</bold>
        <kerning>false</kerning>
       </font>
      </property>
      <property name="text">
       <string>0.0</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_go_to_pose_y">
      <property name="text">
       <string>Y:</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="go_to_pose_y">
      <property name="font">
       <font>
        <pointsize>9</pointsize>
        <weight>50</weight>
        <bold>false</bold>
        <kerning>false</kerning>
       </font>
      </property>
      <property name="text">
       <string>0.0</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_go_to_pose_z">
      <property name="text">
       <string>Z:</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="go_to_pose_z">
      <property name="font">
       <font>
        <pointsize>9</pointsize>
        <weight>50</weight>
        <bold>false</bold>
        <kerning>false</kerning>
       </font>
      </property>
      <property name="text">
       <string>1.0</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLabel" name="label_go_to_pose_heading">
      <property name="text">
       <string>Head. (°):</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QLineEdit" name="go_to_pose_heading">
      <property name="font">
       <font>
        <pointsize>9</pointsize>
        <weight>50</weight>
        <bold>false</bold>
        <kerning>false</kerning>
       </font>
      </property>
      <property name="text">
       <string>0.0</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QPushButton" name="button_go_to_pose">
      <property name="font">
       <font>
        <weight>50</weight>
        <bold>false</bold>
       </font>
      </property>
      <property name="cursor">
       <cursorShape>ArrowCursor</cursorShape>
      </property>
      <property name="text">
       <string>Go To Pose</string>
      </property>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QWidget" name="background_widget" native="true">
   <property name="geometry">
    <rect>
     <x>-10</x>
     <y>0</y>
     <width>551</width>
     <height>691</height>
    </rect>
   </property>
   <widget class="QPushButton" name="button_off">
    <property name="enabled">
     <bool>false</bool>
    </property>
    <property name="geometry">
     <rect>
      <x>230</x>
      <y>30</y>
      <width>281</width>
      <height>71</height>
     </rect>
    </property>
    <property name="font">
     <font>
      <pointsize>22</pointsize>
      <weight>75</weight>
      <bold>true</bold>
     </font>
    </property>
    <property name="cursor">
     <cursorShape>ArrowCursor</cursorShape>
    </property>
    <property name="autoFillBackground">
     <bool>false</bool>
    </property>
    <property name="styleSheet">
     <string notr="true">QPushButton:pressed { 
		    color: #ffffff;
        		border: 3px solid #8f8f91; 
        		border-radius: 6px; 
        		background-color: 
        		qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #ff0000, stop: 1 #ff0000); }
QPushButton:disabled { 
			color: #222222;
        		border: 1px solid #8f8f91; 
        		border-radius: 6px; 
        		background-color: 
        		qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #ff0000, stop: 1 #ff0000); }
QPushButton { 
			color: #000000;
        		border: 1px solid #8f8f91; 
        		border-radius: 6px; 
        		background-color: 
        		qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1, stop: 0 #ff0000, stop: 1 #ff0000); }</string>
    </property>
    <property name="text">
     <string>OFF</string>
    </property>
   </widget>
  </widget>
  <zorder>background_widget</zorder>
  <zorder>layoutWidget</zorder>
  <zorder>label_2</zorder>
  <zorder>label_7</zorder>
  <zorder>pilot_title</zorder>
  <zorder>button_arm_bridge</zorder>
  <zorder>button_land</zorder>
  <zorder>button_start</zorder>
  <zorder>label_9</zorder>
  <zorder>button_force_hover</zorder>
  <zorder>label_13</zorder>
  <zorder>label_10</zorder>
  <zorder>label_12</zorder>
  <zorder>control_computation_time</zorder>
  <zorder>bridge_name</zorder>
  <zorder>legend_battery</zorder>
  <zorder>status_battery_voltage</zorder>
  <zorder>legend_state_est_velocity</zorder>
  <zorder>legend_state_est_orientation</zorder>
  <zorder>legend_state_est_position</zorder>
  <zorder>state_est_orientation</zorder>
  <zorder>state_est_position</zorder>
  <zorder>state_est_velocity</zorder>
  <zorder>legend_state_des_velocity</zorder>
  <zorder>legend_state_des_yaw</zorder>
  <zorder>legend_state_des_acceleration</zorder>
  <zorder>legend_state_des_position</zorder>
  <zorder>ref_orientation</zorder>
  <zorder>ref_position</zorder>
  <zorder>ref_velocity</zorder>
  <zorder>ref_body_rates</zorder>
  <zorder>label_14</zorder>
  <zorder>label_15</zorder>
  <zorder>trajectory_execution_left_duration</zorder>
  <zorder>trajectories_left_in_queue</zorder>
  <zorder>legend_state_est_body_rates</zorder>
  <zorder>state_est_body_rates</zorder>
  <zorder>state_est_frame_id</zorder>
  <zorder>bridge_state</zorder>
  <zorder>click_status</zorder>
 </widget>
 <resources/>
 <connections/>
</ui>
